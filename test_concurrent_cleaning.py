#!/usr/bin/env python3
"""
并发数据清理测试脚本
测试新的并发处理和进度显示功能
"""

import sys
import os
import pandas as pd
from pathlib import Path
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_data():
    """创建测试数据文件"""
    print("=== 创建测试数据 ===")
    
    # 创建临时目录
    test_dir = Path("test_data_cleaning")
    test_dir.mkdir(exist_ok=True)
    
    # 创建多个测试CSV文件
    test_files = []
    
    for i in range(5):
        # 创建包含重复数据和空值的测试数据
        data = {
            'id': list(range(1, 101)) + list(range(50, 150)),  # 包含重复
            'name': [f'用户{j}' for j in range(1, 101)] + [f'用户{j}' for j in range(50, 150)],
            'age': list(range(20, 120)) + list(range(70, 170)),
            'city': ['北京', '上海', '广州', None, '深圳'] * 30,  # 包含空值
            'score': [85.5, 92.0, None, 78.5, 88.0] * 30  # 包含空值
        }
        
        df = pd.DataFrame(data)
        file_path = test_dir / f"test_data_{i+1}.csv"
        df.to_csv(file_path, index=False, encoding='utf-8')
        test_files.append(file_path)
        print(f"   创建测试文件: {file_path} ({len(df)} 行)")
    
    # 创建一个Excel文件
    excel_data = {
        'product': ['产品A', '产品B', '产品A', None, '产品C'] * 20,
        'price': [100, 200, 100, 150, None] * 20,
        'category': ['电子', '服装', '电子', '食品', '电子'] * 20
    }
    excel_df = pd.DataFrame(excel_data)
    excel_file = test_dir / "test_data.xlsx"
    excel_df.to_excel(excel_file, index=False)
    test_files.append(excel_file)
    print(f"   创建Excel文件: {excel_file} ({len(excel_df)} 行)")
    
    # 创建一个JSON文件
    json_data = {
        'user_id': list(range(1, 51)) + list(range(25, 75)),  # 重复数据
        'username': [f'user{i}' for i in range(1, 51)] + [f'user{i}' for i in range(25, 75)],
        'email': [f'user{i}@example.com' if i % 5 != 0 else None for i in range(1, 51)] + 
                [f'user{i}@example.com' if i % 5 != 0 else None for i in range(25, 75)],
        'status': ['active', 'inactive', None, 'pending', 'active'] * 25
    }
    json_df = pd.DataFrame(json_data)
    json_file = test_dir / "test_data.json"
    json_df.to_json(json_file, orient='records', indent=2, force_ascii=False)
    test_files.append(json_file)
    print(f"   创建JSON文件: {json_file} ({len(json_df)} 行)")
    
    print(f"   总计创建 {len(test_files)} 个测试文件")
    return test_dir, test_files

def test_single_file_cleaning():
    """测试单文件清理"""
    print("\n=== 单文件清理测试 ===")
    
    try:
        from click.testing import CliRunner
        from modules.data_cleaning import clean_data
        
        # 创建测试数据
        test_dir, test_files = create_test_data()
        
        # 测试单个CSV文件
        runner = CliRunner()
        csv_file = test_files[0]  # 第一个CSV文件
        
        print(f"测试清理文件: {csv_file}")
        
        result = runner.invoke(clean_data, [
            str(csv_file),
            '--output', str(test_dir / 'output'),
            '--remove-duplicates',
            '--remove-na',
            '--max-workers', '2'
        ])
        
        print(f"命令退出码: {result.exit_code}")
        if result.output:
            print("命令输出:")
            print(result.output)
        
        if result.exception:
            print(f"异常: {result.exception}")
            import traceback
            traceback.print_exception(type(result.exception), result.exception, result.exception.__traceback__)
        
        return result.exit_code == 0
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_directory_cleaning():
    """测试目录批量清理"""
    print("\n=== 目录批量清理测试 ===")
    
    try:
        from click.testing import CliRunner
        from modules.data_cleaning import clean_data
        
        # 创建测试数据
        test_dir, test_files = create_test_data()
        
        print(f"测试清理目录: {test_dir}")
        print(f"包含文件: {len(test_files)} 个")
        
        runner = CliRunner()
        
        result = runner.invoke(clean_data, [
            str(test_dir),
            '--output', str(test_dir / 'batch_output'),
            '--remove-duplicates',
            '--remove-na',
            '--max-workers', '3',
            '--file-extensions', 'csv,xlsx,json'
        ])
        
        print(f"命令退出码: {result.exit_code}")
        if result.output:
            print("命令输出:")
            print(result.output)
        
        if result.exception:
            print(f"异常: {result.exception}")
            import traceback
            traceback.print_exception(type(result.exception), result.exception, result.exception.__traceback__)
        
        return result.exit_code == 0
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_queue_task_directly():
    """直接测试任务队列"""
    print("\n=== 任务队列直接测试 ===")
    
    try:
        from core.queue_task import AsyncTaskQueue, QueueConfig, run_async_task
        import asyncio
        import time
        
        # 创建简单的测试任务
        def simple_task(data):
            """简单的测试任务"""
            time.sleep(0.1)  # 模拟处理时间
            return f"处理完成: {data}"
        
        # 创建测试数据
        test_data = [f"任务{i}" for i in range(10)]
        
        print(f"创建 {len(test_data)} 个测试任务")
        
        # 配置队列
        config = QueueConfig(
            max_workers=3,
            max_retries=1,
            progress_description="测试任务处理"
        )
        
        # 创建队列并处理任务
        queue = AsyncTaskQueue(config)
        
        async def run_test():
            results = await queue.process_tasks(simple_task, test_data)
            queue.display_summary()
            return results
        
        results = run_async_task(run_test())
        
        success_count = sum(1 for r in results if r.status.value == 'success')
        print(f"成功处理: {success_count}/{len(results)} 个任务")
        
        return success_count == len(results)
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    test_dir = Path("test_data_cleaning")
    if test_dir.exists():
        shutil.rmtree(test_dir)
        print(f"   已删除测试目录: {test_dir}")
    else:
        print("   测试目录不存在，无需清理")

def main():
    """主测试函数"""
    print("并发数据清理功能测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("任务队列直接测试", test_queue_task_directly),
        ("单文件清理测试", test_single_file_cleaning),
        ("目录批量清理测试", test_directory_cleaning),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n开始执行: {test_name}")
            result = test_func()
            results.append((test_name, result))
            print(f"测试结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    # 清理测试数据
    cleanup_test_data()
    
    if passed == len(results):
        print("\n🎉 所有测试通过！并发数据清理功能正常工作。")
    else:
        print("\n⚠️  部分测试失败，请检查相关问题。")

if __name__ == '__main__':
    main()
