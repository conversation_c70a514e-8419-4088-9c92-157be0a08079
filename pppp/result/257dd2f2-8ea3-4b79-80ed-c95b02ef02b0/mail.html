<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件内容</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .email-header { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .email-field { margin-bottom: 8px; }
        .email-field strong { color: #333; min-width: 80px; display: inline-block; }
        .email-body { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .attachments { margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-radius: 5px; }
        .attachments ul { margin: 5px 0; padding-left: 20px; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="email-header">
        <div class="email-field"><strong>主题:</strong> OWI-16</div>
        <div class="email-field"><strong>发件人:</strong> <EMAIL></div>
        <div class="email-field"><strong>收件人:</strong> asamericas1 &lt;<EMAIL>&gt;, asamericas &lt;<EMAIL>&gt;, dir c&amp;la1 &lt;dir.c&amp;<EMAIL>&gt;, &quot;dir.c&amp;la&quot; &lt;dir.c&amp;<EMAIL>&gt;</div>
        
        <div class="email-field"><strong>日期:</strong> Wed, 11 Dec 2024 01:24:31 +0500</div>
    </div>

    <div class="email-body">
        <div id="zimbraEditorContainer" style="font-family: arial, helvetica, sans-serif; font-size: 12pt; color: #000000" class="1"><div><style type="text/css" scoped="">

<!--

 /* Font Definitions */

 @font-face

	{font-family:"Cambria Math";

	panose-1:2 4 5 3 5 4 6 3 2 4;

	mso-font-charset:0;

	mso-generic-font-family:roman;

	mso-font-pitch:variable;

	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}

@font-face

	{font-family:Calibri;

	panose-1:2 15 5 2 2 2 4 3 2 4;

	mso-font-charset:0;

	mso-generic-font-family:swiss;

	mso-font-pitch:variable;

	mso-font-signature:-469750017 -1073732485 9 0 511 0;}

 /* Style Definitions */

 p.MsoNormal, li.MsoNormal, div.MsoNormal

	{mso-style-unhide:no;

	mso-style-qformat:yes;

	mso-style-parent:"";

	margin-top:0cm;

	margin-right:0cm;

	margin-bottom:8.0pt;

	margin-left:0cm;

	line-height:107%;

	mso-pagination:widow-orphan;

	font-size:11.0pt;

	font-family:"Calibri",sans-serif;

	mso-fareast-font-family:Calibri;

	mso-bidi-font-family:"Times New Roman";

	mso-fareast-language:EN-US;}

a:link, span.MsoHyperlink

	{mso-style-priority:99;

	mso-style-parent:"";

	color:blue;

	text-decoration:underline;

	text-underline:single;}

a:visited, span.MsoHyperlinkFollowed

	{mso-style-noshow:yes;

	mso-style-priority:99;

	color:#954F72;

	mso-themecolor:followedhyperlink;

	text-decoration:underline;

	text-underline:single;}

.zimbra1

	{mso-style-type:export-only;

	mso-default-props:yes;

	font-size:10.0pt;

	mso-ansi-font-size:10.0pt;

	mso-bidi-font-size:10.0pt;

	font-family:"Calibri",sans-serif;

	mso-ascii-font-family:Calibri;

	mso-fareast-font-family:Calibri;

	mso-hansi-font-family:Calibri;

	mso-font-kerning:0pt;

	mso-ligatures:none;}

@page WordSection1

	{size:612.0pt 792.0pt;

	margin:72.0pt 72.0pt 72.0pt 72.0pt;

	mso-header-margin:36.0pt;

	mso-footer-margin:36.0pt;

	mso-paper-source:0;}

div.WordSection1

	{page:WordSection1;}

-->

</style></div><div> <!-- [if gte mso 9]><xml>

 <o:OfficeDocumentSettings>

  <o:TargetScreenSize>800x600</o:TargetScreenSize>

 </o:OfficeDocumentSettings>

</xml><![endif]-->   <!-- [if gte mso 9]><xml>

 <w:WordDocument>

  <w:View>Normal</w:View>

  <w:Zoom>0</w:Zoom>

  <w:TrackMoves/>

  <w:TrackFormatting/>

  <w:PunctuationKerning/>

  <w:ValidateAgainstSchemas/>

  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>

  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>

  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>

  <w:DoNotPromoteQF/>

  <w:LidThemeOther>EN-CA</w:LidThemeOther>

  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>

  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>

  <w:Compatibility>

   <w:BreakWrappedTables/>

   <w:SnapToGridInCell/>

   <w:WrapTextWithPunct/>

   <w:UseAsianBreakRules/>

   <w:DontGrowAutofit/>

   <w:SplitPgBreakAndParaMark/>

   <w:EnableOpenTypeKerning/>

   <w:DontFlipMirrorIndents/>

   <w:OverrideTableStyleHps/>

  </w:Compatibility>

  <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>

  <m:mathPr>

   <m:mathFont m:val="Cambria Math"/>

   <m:brkBin m:val="before"/>

   <m:brkBinSub m:val="&#45;-"/>

   <m:smallFrac m:val="off"/>

   <m:dispDef/>

   <m:lMargin m:val="0"/>

   <m:rMargin m:val="0"/>

   <m:defJc m:val="centerGroup"/>

   <m:wrapIndent m:val="1440"/>

   <m:intLim m:val="subSup"/>

   <m:naryLim m:val="undOvr"/>

  </m:mathPr></w:WordDocument>

</xml><![endif]--><!-- [if gte mso 9]><xml>

 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"

  DefSemiHidden="false" DefQFormat="false" DefPriority="99"

  LatentStyleCount="376">

  <w:LsdException Locked="false" Priority="0" QFormat="true" Name="Normal"/>

  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 1"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 2"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 3"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 4"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 5"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 6"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 7"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 8"/>

  <w:LsdException Locked="false" Priority="9" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="heading 9"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 6"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 7"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 8"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index 9"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 1"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 2"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 3"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 4"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 5"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 6"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 7"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 8"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" Name="toc 9"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Normal Indent"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="footnote text"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="annotation text"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="header"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="footer"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="index heading"/>

  <w:LsdException Locked="false" Priority="35" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="caption"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="table of figures"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="envelope address"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="envelope return"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="footnote reference"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="annotation reference"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="line number"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="page number"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="endnote reference"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="endnote text"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="table of authorities"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="macro"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="toa heading"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Bullet"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Number"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Bullet 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Bullet 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Bullet 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Bullet 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Number 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Number 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Number 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Number 5"/>

  <w:LsdException Locked="false" Priority="10" QFormat="true" Name="Title"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Closing"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Signature"/>

  <w:LsdException Locked="false" Priority="1" SemiHidden="true"

   UnhideWhenUsed="true" Name="Default Paragraph Font"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text Indent"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Continue"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Continue 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Continue 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Continue 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="List Continue 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Message Header"/>

  <w:LsdException Locked="false" Priority="11" QFormat="true" Name="Subtitle"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Salutation"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Date"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text First Indent"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text First Indent 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Note Heading"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text Indent 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Body Text Indent 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Block Text"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Hyperlink"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="FollowedHyperlink"/>

  <w:LsdException Locked="false" Priority="22" QFormat="true" Name="Strong"/>

  <w:LsdException Locked="false" Priority="20" QFormat="true" Name="Emphasis"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Document Map"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Plain Text"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="E-mail Signature"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Top of Form"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Bottom of Form"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Normal (Web)"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Acronym"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Address"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Cite"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Code"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Definition"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Keyboard"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Preformatted"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Sample"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Typewriter"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="HTML Variable"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Normal Table"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="annotation subject"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="No List"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Outline List 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Outline List 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Outline List 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Simple 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Simple 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Simple 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Classic 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Classic 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Classic 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Classic 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Colorful 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Colorful 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Colorful 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Columns 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Columns 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Columns 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Columns 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Columns 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 6"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 7"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Grid 8"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 4"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 5"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 6"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 7"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table List 8"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table 3D effects 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table 3D effects 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table 3D effects 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Contemporary"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Elegant"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Professional"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Subtle 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Subtle 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Web 1"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Web 2"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Web 3"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Balloon Text"/>

  <w:LsdException Locked="false" Priority="39" Name="Table Grid"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Table Theme"/>

  <w:LsdException Locked="false" SemiHidden="true" Name="Placeholder Text"/>

  <w:LsdException Locked="false" Priority="1" QFormat="true" Name="No Spacing"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>

  <w:LsdException Locked="false" SemiHidden="true" Name="Revision"/>

  <w:LsdException Locked="false" Priority="34" QFormat="true"

   Name="List Paragraph"/>

  <w:LsdException Locked="false" Priority="29" QFormat="true" Name="Quote"/>

  <w:LsdException Locked="false" Priority="30" QFormat="true"

   Name="Intense Quote"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>

  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>

  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>

  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>

  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>

  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>

  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>

  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>

  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>

  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>

  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>

  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>

  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>

  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>

  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>

  <w:LsdException Locked="false" Priority="19" QFormat="true"

   Name="Subtle Emphasis"/>

  <w:LsdException Locked="false" Priority="21" QFormat="true"

   Name="Intense Emphasis"/>

  <w:LsdException Locked="false" Priority="31" QFormat="true"

   Name="Subtle Reference"/>

  <w:LsdException Locked="false" Priority="32" QFormat="true"

   Name="Intense Reference"/>

  <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>

  <w:LsdException Locked="false" Priority="37" SemiHidden="true"

   UnhideWhenUsed="true" Name="Bibliography"/>

  <w:LsdException Locked="false" Priority="39" SemiHidden="true"

   UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>

  <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>

  <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>

  <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>

  <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>

  <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>

  <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>

  <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>

  <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>

  <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>

  <w:LsdException Locked="false" Priority="46"

   Name="Grid Table 1 Light Accent 1"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>

  <w:LsdException Locked="false" Priority="51"

   Name="Grid Table 6 Colorful Accent 1"/>

  <w:LsdException Locked="false" Priority="52"

   Name="Grid Table 7 Colorful Accent 1"/>

  <w:LsdException Locked="false" Priority="46"

   Name="Grid Table 1 Light Accent 2"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>

  <w:LsdException Locked="false" Priority="51"

   Name="Grid Table 6 Colorful Accent 2"/>

  <w:LsdException Locked="false" Priority="52"

   Name="Grid Table 7 Colorful Accent 2"/>

  <w:LsdException Locked="false" Priority="46"

   Name="Grid Table 1 Light Accent 3"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>

  <w:LsdException Locked="false" Priority="51"

   Name="Grid Table 6 Colorful Accent 3"/>

  <w:LsdException Locked="false" Priority="52"

   Name="Grid Table 7 Colorful Accent 3"/>

  <w:LsdException Locked="false" Priority="46"

   Name="Grid Table 1 Light Accent 4"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>

  <w:LsdException Locked="false" Priority="51"

   Name="Grid Table 6 Colorful Accent 4"/>

  <w:LsdException Locked="false" Priority="52"

   Name="Grid Table 7 Colorful Accent 4"/>

  <w:LsdException Locked="false" Priority="46"

   Name="Grid Table 1 Light Accent 5"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>

  <w:LsdException Locked="false" Priority="51"

   Name="Grid Table 6 Colorful Accent 5"/>

  <w:LsdException Locked="false" Priority="52"

   Name="Grid Table 7 Colorful Accent 5"/>

  <w:LsdException Locked="false" Priority="46"

   Name="Grid Table 1 Light Accent 6"/>

  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>

  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>

  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>

  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>

  <w:LsdException Locked="false" Priority="51"

   Name="Grid Table 6 Colorful Accent 6"/>

  <w:LsdException Locked="false" Priority="52"

   Name="Grid Table 7 Colorful Accent 6"/>

  <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>

  <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>

  <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>

  <w:LsdException Locked="false" Priority="46"

   Name="List Table 1 Light Accent 1"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>

  <w:LsdException Locked="false" Priority="51"

   Name="List Table 6 Colorful Accent 1"/>

  <w:LsdException Locked="false" Priority="52"

   Name="List Table 7 Colorful Accent 1"/>

  <w:LsdException Locked="false" Priority="46"

   Name="List Table 1 Light Accent 2"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>

  <w:LsdException Locked="false" Priority="51"

   Name="List Table 6 Colorful Accent 2"/>

  <w:LsdException Locked="false" Priority="52"

   Name="List Table 7 Colorful Accent 2"/>

  <w:LsdException Locked="false" Priority="46"

   Name="List Table 1 Light Accent 3"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>

  <w:LsdException Locked="false" Priority="51"

   Name="List Table 6 Colorful Accent 3"/>

  <w:LsdException Locked="false" Priority="52"

   Name="List Table 7 Colorful Accent 3"/>

  <w:LsdException Locked="false" Priority="46"

   Name="List Table 1 Light Accent 4"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>

  <w:LsdException Locked="false" Priority="51"

   Name="List Table 6 Colorful Accent 4"/>

  <w:LsdException Locked="false" Priority="52"

   Name="List Table 7 Colorful Accent 4"/>

  <w:LsdException Locked="false" Priority="46"

   Name="List Table 1 Light Accent 5"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>

  <w:LsdException Locked="false" Priority="51"

   Name="List Table 6 Colorful Accent 5"/>

  <w:LsdException Locked="false" Priority="52"

   Name="List Table 7 Colorful Accent 5"/>

  <w:LsdException Locked="false" Priority="46"

   Name="List Table 1 Light Accent 6"/>

  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>

  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>

  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>

  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>

  <w:LsdException Locked="false" Priority="51"

   Name="List Table 6 Colorful Accent 6"/>

  <w:LsdException Locked="false" Priority="52"

   Name="List Table 7 Colorful Accent 6"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Mention"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Smart Hyperlink"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Hashtag"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Unresolved Mention"/>

  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"

   Name="Smart Link"/>

 </w:LatentStyles>

</xml><![endif]--><!-- [if gte mso 10]>

<style>

 /* Style Definitions */

 table.MsoNormalTable

	{mso-style-name:"Table Normal";

	mso-tstyle-rowband-size:0;

	mso-tstyle-colband-size:0;

	mso-style-noshow:yes;

	mso-style-priority:99;

	mso-style-parent:"";

	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;

	mso-para-margin:0cm;

	mso-pagination:widow-orphan;

	font-size:10.0pt;

	font-family:"Calibri",sans-serif;

	mso-bidi-font-family:"Times New Roman";}

</style>

<![endif]--> <!--StartFragment--><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">Best Regards,<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;"><o:p>&nbsp;</o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif; mso-fareast-font-family: 'Times New Roman'; color: black; mso-fareast-language: EN-CA;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif; mso-fareast-font-family: 'Times New Roman'; color: black; mso-fareast-language: EN-CA;">Shah Faisal Kakar,<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif; mso-fareast-font-family: 'Times New Roman'; color: black; mso-fareast-language: EN-CA;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif; mso-fareast-font-family: 'Times New Roman'; color: black; mso-fareast-language: EN-CA;">Acting High Commissioner<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">High Commission for Pakistan<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">10 Range Road<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">Ottawa, ON K1N 8J3<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">Tel: +1 (613)238-7881/82/83<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">Fax: +1 (613)238-7296<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">E-Mail: </span><span lang="FR" style="font-size: 12.0pt; font-family: 'Times New Roman',serif; color: black; mso-ansi-language: FR;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif; color: black; mso-ansi-language: FR;"><a href="mailto:<EMAIL>" data-mce-href="mailto:<EMAIL>"><span lang="EN-CA" style="color: black; mso-ansi-language: EN-CA;" data-mce-style="color: black; mso-ansi-language: EN-CA;"><EMAIL></span></a></span><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;"><o:p></o:p></span></p><p class="MsoNormal" style="margin: 0px; line-height: normal;" data-mce-style="margin: 0px; line-height: normal;"><span style="font-size: 12.0pt; font-family: 'Times New Roman',serif;" data-mce-style="font-size: 12.0pt; font-family: 'Times New Roman',serif;">Website: www.mofa.gov.pk/ottawa<o:p></o:p></span></p><!--EndFragment--> </div></div>

<br><html><body>&nbsp;<hr/>&nbsp;<b>MOFA Disclaimer</b>: The contents of this email and any attachments are confidential. They are intended for the named recipient(s) only. If you have received this email by mistake, please notify the sender immediately and do not disclose the contents to anyone or make any copies thereof.
    </div>
</body>
</html>