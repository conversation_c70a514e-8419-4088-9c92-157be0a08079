#!/usr/bin/env python3
"""
CLI帮助信息测试脚本
测试Click命令参数是否正确显示在帮助信息中
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_click_command_detection():
    """测试Click命令检测"""
    print("=== Click命令检测测试 ===")
    
    try:
        # 导入模块以触发注册
        from modules import data_cleaning
        from core.registry import registry
        
        # 获取注册的模块
        module_info = registry.get_module('data_cleaning')
        if not module_info:
            print("   ❌ data_cleaning模块未注册")
            return False
        
        handler = module_info.handler
        print(f"   模块ID: {module_info.id}")
        print(f"   模块名称: {module_info.name}")
        print(f"   处理函数: {handler.__name__}")
        
        # 检查是否是Click命令
        if hasattr(handler, '__click_params__'):
            print("   ✅ 检测到Click命令")
            print(f"   Click参数数量: {len(handler.__click_params__)}")
            
            # 显示参数详情
            for i, param in enumerate(handler.__click_params__):
                param_type = type(param).__name__
                param_name = getattr(param, 'name', 'unknown')
                param_help = getattr(param, 'help', 'no help')
                print(f"     参数{i+1}: {param_name} ({param_type}) - {param_help}")
        else:
            print("   ❌ 未检测到Click命令")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_cli_framework_registration():
    """测试CLI框架注册"""
    print("\n=== CLI框架注册测试 ===")
    
    try:
        from core.cli_framework import DynamicCLI
        
        print("1. 创建CLI实例")
        cli = DynamicCLI()
        
        print("2. 检查主命令组")
        main_group = cli.main_group
        print(f"   主命令组: {main_group.name}")
        print(f"   现有命令数量: {len(main_group.commands)}")
        
        print("3. 注册授权模块")
        # 模拟授权模块
        cli.cli_context.authorized_modules = ['data_cleaning']
        cli.register_all_authorized_modules()
        
        print(f"   注册后命令数量: {len(main_group.commands)}")
        
        # 检查data_cleaning命令是否存在
        if 'data_cleaning' in main_group.commands:
            print("   ✅ data_cleaning命令已注册")
            
            # 获取命令对象
            data_cleaning_cmd = main_group.commands['data_cleaning']
            print(f"   命令名称: {data_cleaning_cmd.name}")
            print(f"   命令帮助: {data_cleaning_cmd.help}")
            
            # 检查参数
            if hasattr(data_cleaning_cmd, 'params'):
                print(f"   参数数量: {len(data_cleaning_cmd.params)}")
                for param in data_cleaning_cmd.params:
                    param_type = type(param).__name__
                    param_name = getattr(param, 'name', 'unknown')
                    print(f"     - {param_name} ({param_type})")
            else:
                print("   ❌ 命令没有参数信息")
                return False
        else:
            print("   ❌ data_cleaning命令未注册")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_help_output():
    """测试帮助输出"""
    print("\n=== 帮助输出测试 ===")
    
    try:
        import click
        from click.testing import CliRunner
        from core.cli_framework import cli_app
        
        print("1. 测试主帮助")
        runner = CliRunner()
        
        # 设置模拟的授权模块
        cli_app.cli_context.authorized_modules = ['data_cleaning']
        cli_app.register_all_authorized_modules()
        
        # 测试主帮助
        result = runner.invoke(cli_app.main_group, ['--help'])
        print(f"   主帮助退出码: {result.exit_code}")
        if result.output:
            print("   主帮助输出:")
            for line in result.output.split('\n')[:10]:  # 显示前10行
                if line.strip():
                    print(f"     {line}")
        
        print("\n2. 测试data_cleaning帮助")
        result = runner.invoke(cli_app.main_group, ['data_cleaning', '--help'])
        print(f"   data_cleaning帮助退出码: {result.exit_code}")
        if result.output:
            print("   data_cleaning帮助输出:")
            for line in result.output.split('\n'):
                if line.strip():
                    print(f"     {line}")
        else:
            print("   ❌ 没有帮助输出")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_click_command():
    """调试Click命令"""
    print("\n=== Click命令调试 ===")
    
    try:
        from modules.data_cleaning import clean_data
        import click
        
        print("1. 直接检查clean_data函数")
        print(f"   函数名: {clean_data.__name__}")
        print(f"   是否有__click_params__: {hasattr(clean_data, '__click_params__')}")
        
        if hasattr(clean_data, '__click_params__'):
            params = clean_data.__click_params__
            print(f"   参数数量: {len(params)}")
            
            for i, param in enumerate(params):
                print(f"   参数{i+1}:")
                print(f"     类型: {type(param).__name__}")
                print(f"     名称: {getattr(param, 'name', 'N/A')}")
                print(f"     帮助: {getattr(param, 'help', 'N/A')}")
                if hasattr(param, 'opts'):
                    print(f"     选项: {param.opts}")
        
        print("\n2. 测试直接调用Click命令")
        from click.testing import CliRunner
        runner = CliRunner()
        
        # 直接测试clean_data命令
        result = runner.invoke(clean_data, ['--help'])
        print(f"   直接调用退出码: {result.exit_code}")
        if result.output:
            print("   直接调用输出:")
            for line in result.output.split('\n'):
                if line.strip():
                    print(f"     {line}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("CLI帮助信息测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("Click命令检测", test_click_command_detection),
        ("CLI框架注册", test_cli_framework_registration),
        ("帮助输出", test_help_output),
        ("Click命令调试", debug_click_command),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！CLI帮助系统应该正常工作。")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试。")

if __name__ == '__main__':
    main()
