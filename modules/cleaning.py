import asyncio
import json
import os
from concurrent.futures import Thread<PERSON>oolExecutor
from pathlib import Path
from typing import List, Dict, Any, Optional

from websockets import SecurityError

from core.exceptions import FilePathNotFoundError
from core.log import setup_logger
from core.queue_task import QueueConfig, AsyncTaskQueue, run_async_task
from core.setting import tool_settings
from utils.encrypted import is_file_encrypted, calculate_file_hash
from utils.file import copy_file, extract_zip_rar_7z, get_document_metadata
from utils.mail import parse_eml

logger = setup_logger(__name__)


class DataCleaner:
    """数据清理器类，负责文件清理和预处理"""

    def __init__(self, input_path: str, output_path: str):
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.file_info = []
        self.file_counter = 1
        self.error_count = 0
        self.success_count = 0
        self._setup_directories()

    def _setup_directories(self):
        """设置输出目录结构"""
        self.output_path.mkdir(parents=True, exist_ok=True)
        self.password_detected_path = self.output_path / "encrypted_document"
        self.error_files_path = self.output_path / "error_files"
        self.result_path = self.output_path / "result"

        for path in [self.password_detected_path, self.error_files_path, self.result_path]:
            path.mkdir(exist_ok=True)

    def process(self):
        """执行数据清理流程"""
        if not self.input_path.exists():
            raise FilePathNotFoundError(str(self.input_path))

        # 收集待处理文件
        task_data = self._collect_files()

        # 处理文件
        self._process_files(task_data)

        # 保存结果
        self._save_results()

    def _collect_files(self) -> List[Dict[str, Any]]:
        """收集需要处理的文件"""
        task_data = []

        if self.input_path.is_file():
            task_data.append(self._create_task_data(str(self.input_path)))
        else:
            task_data.extend(self._collect_directory_files())

        return task_data

    def _collect_directory_files(self) -> List[Dict[str, Any]]:
        """收集目录中的文件"""
        task_data = []
        ext_blacklist = {".meta"}
        supported_archives = {".zip", ".rar", ".7z"}

        for file_path in self.input_path.rglob("*"):
            if not file_path.is_file():
                continue

            # 跳过黑名单文件
            if file_path.suffix in ext_blacklist:
                continue

            # 检查文件访问权限
            if not os.access(file_path, os.R_OK):
                logger.error(f"无法访问文件: {file_path}")
                continue

            # 处理压缩文件
            if file_path.suffix in supported_archives:
                task_data.extend(self._handle_archive_file(file_path))
            else:
                task_data.append(self._create_task_data(str(file_path)))

        return task_data

    def _handle_archive_file(self, archive_path: Path) -> List[Dict[str, Any]]:
        """处理压缩文件"""
        task_data = []

        try:
            # 检查是否加密
            if is_file_encrypted(str(archive_path)):
                self._move_encrypted_file(archive_path)
                return task_data

            # 解压文件
            logger.info(f"正在解压文件: {archive_path}")
            extract_dir = self.input_path / archive_path.stem

            try:
                unpack_files = extract_zip_rar_7z(str(archive_path), str(extract_dir))

                for unpack_file in unpack_files:
                    unpack_path = Path(unpack_file)
                    if not unpack_path.exists():
                        logger.warning(f"解压后文件不存在: {unpack_file}")
                        continue

                    if not os.access(unpack_path, os.R_OK):
                        logger.warning(f"无法访问解压文件: {unpack_file}")
                        continue

                    task_data.append(self._create_task_data(str(unpack_path)))

            except Exception as e:
                logger.error(f"解压文件失败 {archive_path}: {str(e)}")
                # 将失败的压缩文件移动到错误目录
                self._move_error_file(archive_path, f"解压失败: {str(e)}")

        except Exception as e:
            logger.error(f"处理压缩文件时发生未知错误 {archive_path}: {str(e)}")
            self._move_error_file(archive_path, f"处理失败: {str(e)}")

        return task_data

    def _move_encrypted_file(self, file_path: Path):
        """移动加密文件到指定目录"""
        try:
            dest_path = self.password_detected_path / file_path.name
            copy_file(str(file_path), str(dest_path))
            logger.warning(f"检测到加密文件: {file_path}")
        except Exception as e:
            logger.error(f"移动加密文件失败 {file_path}: {str(e)}")

    def _move_error_file(self, file_path: Path, error_msg: str):
        """移动错误文件到错误目录"""
        try:
            dest_path = self.error_files_path / file_path.name
            copy_file(str(file_path), str(dest_path))
            logger.error(f"文件处理错误 {file_path}: {error_msg}")
        except Exception as e:
            logger.error(f"移动错误文件失败 {file_path}: {str(e)}")

    async def process_file(self, task_data: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """处理单个文件（优化版本）"""
        file_path = Path(task_data['file_path'])
        await asyncio.sleep(1)
        try:
            # 预先检查文件状态
            if not file_path.exists():
                logger.warning(f"文件不存在: {file_path}")
                return None

            file_record = {
                'old_file_path': str(file_path)
            }

            # 根据文件类型分发处理
            if file_path.suffix.lower() == '.eml':
                self.file_info.append(await self._process_email_file(file_path, file_record))
            else:
                self.file_info.append(await self._process_regular_file(file_path, file_record))

        except Exception as e:
            logger.error(f"处理文件时出错 {file_path}: {str(e)}")
            await self._handle_file_error(file_path, str(e))
            return None

    async def _process_email_file(self, file_path: Path, file_record: Dict) -> Dict[str, Any]:
        """处理邮件文件"""
        email_save_path = self.result_path / file_path.stem
        # email_save_path.mkdir(exist_ok=True)
        os.makedirs(email_save_path, exist_ok=True)

        file_record['new_file_path'] = str(email_save_path)
        file_record['file_type'] = 'email'

        try:
            mail_info = parse_eml(str(file_path))

            # 保存邮件HTML内容
            mail_html_file = email_save_path / 'mail.html'
            mail_html_file.write_text(mail_info['mail_html'], encoding='utf-8')

            # 处理附件
            attachments = []
            for attachment in mail_info['attachments']:
                att_info = await self._process_email_attachment(
                    attachment, email_save_path
                )
                if att_info:
                    attachments.append(att_info)

            file_record['attachments'] = attachments
            return file_record

        except Exception as e:
            logger.error(f"处理邮件文件失败 {file_path}: {str(e)}")
            raise

    async def _process_email_attachment(self, attachment: Dict, save_dir: Path) -> Optional[Dict[str, Any]]:
        """处理邮件附件"""
        try:
            filename = attachment.get('filename', 'unknown')
            content = attachment.get('content', b'')

            if not content:
                logger.warning(f"附件内容为空: {filename}")
                return None

            # 生成新文件名
            file_ext = Path(filename).suffix
            new_name = f"{self.file_counter:04d}{file_ext}"
            self.file_counter += 1

            att_info = {
                'filename': filename,
                'new_filename': new_name,
                'encrypted': False,
                'size': len(content)
            }

            # 临时保存附件用于检测
            temp_path = save_dir / new_name
            temp_path.write_bytes(content)

            # 检测是否加密（修正：检测附件本身而不是邮件文件）
            if is_file_encrypted(str(temp_path)):
                att_info['encrypted'] = True
                # 移动到加密文件目录
                encrypted_path = self.password_detected_path / new_name
                temp_path.rename(encrypted_path)
                print(f"检测到加密附件: {filename}")

            return att_info

        except Exception as e:
            logger.error(f"处理邮件附件失败 {attachment.get('filename', 'unknown')}: {str(e)}")
            return None

    def _create_task_data(self, file_path: str) -> Dict[str, Any]:
        """创建任务数据，包含安全检查"""
        file_path_obj = Path(file_path)

        # 安全检查：确保文件路径在允许的范围内
        try:
            file_path_obj.resolve().relative_to(self.input_path.resolve())
        except ValueError:
            logger.warning(f"文件路径超出允许范围: {file_path}")
            raise SecurityError(f"不安全的文件路径: {file_path}")

        return {
            'file_path': str(file_path_obj),
            'password_detected_path': str(self.password_detected_path),
            'error_files_path': str(self.error_files_path),
            'result_path': str(self.result_path)
        }

    def _process_files(self, task_data: List[Dict[str, Any]]):
        """批量处理文件，使用异步并发提高效率"""
        if not task_data:
            logger.info("没有文件需要处理")
            return

        logger.info(f"开始处理 {len(task_data)} 个文件")
        config = QueueConfig(
            max_workers=tool_settings.tool_config.limits,
            max_retries=2,
            progress_description="数据清洗任务",
            show_progress=True,
            show_detailed_progress=True
        )

        # 创建队列并处理任务
        queue = AsyncTaskQueue(config)
        async def run_test():
            return await queue.process_tasks(self.process_file, task_data)

        run_async_task(run_test())

        # 显示结果摘要
        queue.display_summary()

    async def _process_regular_file(self, file_path: Path, file_record: Dict) -> Dict[str, Any]:
        """处理非邮件类型的普通文件"""
        try:
            # 检测文件是否加密
            if is_file_encrypted(str(file_path)):
                # 加密文件处理
                encrypted_dest = self.password_detected_path / file_path.name
                copy_file(str(file_path), str(encrypted_dest))
                
                file_record.update({
                    'new_file_path': str(encrypted_dest),
                    'file_type': 'encrypted',
                    'encrypted': True,
                    'file_size': file_path.stat().st_size
                })
                
                logger.info(f"检测到加密文件，已移动到: {encrypted_dest}")
                return file_record
            
            # 生成新文件名
            file_ext = file_path.suffix
            new_file_name = f"{self.file_counter:04d}{file_ext}"
            new_file_path = self.result_path / new_file_name
            self.file_counter += 1
            
            # 复制文件到结果目录
            copy_file(str(file_path), str(new_file_path))
            
            # 获取文件基本信息
            file_stat = file_path.stat()
            
            # 异步获取文件元数据和哈希值
            metadata_task = asyncio.create_task(
                self._get_file_metadata_async(str(file_path))
            )
            hash_task = asyncio.create_task(
                self._calculate_file_hash_async(str(file_path))
            )
            
            # 等待元数据和哈希值计算完成
            metadata, file_hash = await asyncio.gather(
                metadata_task, hash_task, return_exceptions=True
            )
            
            # 处理异常结果
            if isinstance(metadata, Exception):
                logger.warning(f"获取文件元数据失败 {file_path}: {metadata}")
                metadata = {}
            
            if isinstance(file_hash, Exception):
                logger.warning(f"计算文件哈希失败 {file_path}: {file_hash}")
                file_hash = None
            
            # 更新文件记录
            file_record.update({
                'new_file_path': str(new_file_path),
                'new_filename': new_file_name,
                'file_type': 'regular',
                'encrypted': False,
                'file_size': file_stat.st_size,
                'metadata': metadata,
                'hash': file_hash,
                'created_time': file_stat.st_ctime,
                'modified_time': file_stat.st_mtime
            })
            
            logger.debug(f"成功处理普通文件: {file_path} -> {new_file_path}")
            return file_record
            
        except Exception as e:
            logger.error(f"处理普通文件失败 {file_path}: {str(e)}")
            raise

    async def _handle_file_error(self, file_path: Path, error_msg: str):
        """处理文件处理过程中出现的错误"""
        try:
            # 生成错误文件的目标路径
            error_dest = self.error_files_path / file_path.name
            
            # 如果目标文件已存在，添加时间戳避免冲突
            if error_dest.exists():
                timestamp = int(asyncio.get_event_loop().time())
                stem = file_path.stem
                suffix = file_path.suffix
                error_dest = self.error_files_path / f"{stem}_{timestamp}{suffix}"
            
            # 复制错误文件到错误目录
            copy_file(str(file_path), str(error_dest))
            
            # 记录详细错误信息
            error_record = {
                'original_path': str(file_path),
                'error_path': str(error_dest),
                'error_message': error_msg,
                'error_time': asyncio.get_event_loop().time(),
                'file_size': file_path.stat().st_size if file_path.exists() else 0
            }
            
            # 可以选择将错误记录也添加到file_info中，或单独记录
            logger.error(f"文件处理错误 - 原文件: {file_path}, 错误: {error_msg}, 已移动到: {error_dest}")
            
            # 更新错误统计
            self.error_count += 1
            
        except Exception as e:
            # 处理移动错误文件时的异常
            logger.critical(f"无法处理错误文件 {file_path}: {str(e)}")
            # 即使移动失败，也要记录原始错误
            logger.error(f"原始文件处理错误: {file_path} - {error_msg}")

    async def _get_file_metadata_async(self, file_path: str) -> Dict[str, Any]:
        """异步获取文件元数据"""
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as executor:
            return await loop.run_in_executor(executor, get_document_metadata, file_path)

    async def _calculate_file_hash_async(self, file_path: str) -> str:
        """异步计算文件哈希值"""
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as executor:
            return await loop.run_in_executor(executor, calculate_file_hash, file_path)

    def _save_results(self):
        """安全保存处理结果"""
        json_file_path = self.output_path / "file_records.json"

        try:
            # 确保输出目录存在且可写
            if not os.access(self.output_path, os.W_OK):
                raise PermissionError(f"无法写入输出目录: {self.output_path}")

            # 原子写入：先写入临时文件，再重命名
            # temp_file = json_file_path.with_suffix('.tmp')

            with json_file_path.open('w', encoding='utf-8') as f:
                json.dump(self.file_info, f, ensure_ascii=False, indent=4)

            logger.info(f"处理结果已保存到: {json_file_path}")

        except Exception as e:
            logger.error(f"保存文件记录失败: {str(e)}")