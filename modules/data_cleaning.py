"""
数据清理模块
提供数据清理和预处理功能，支持并发处理和进度显示
"""

import os
import pandas as pd
import click
import asyncio
from pathlib import Path
from typing import Optional, List, Union, Dict, Any

from core.registry import registry
from core.exceptions import *
from core.queue_task import AsyncTaskQueue, QueueConfig, TaskResult, run_async_task

@click.command()
@click.argument('input_file', type=click.Path(exists=True, readable=True))
@click.option('-o', '--output', 'output_file',
              type=click.Path(),
              help='输出文件路径（对于目录处理，这将是输出目录）')
@click.option('--remove-duplicates/--keep-duplicates',
              default=True,
              help='是否删除重复行')
@click.option('--remove-na/--keep-na',
              default=True,
              help='是否删除空值行')
@click.option('--max-workers', default=10, type=int,
              help='最大并发处理数量')
@click.option('--file-extensions', default='csv,xlsx,json',
              help='要处理的文件扩展名（逗号分隔）')
@handle_exception
def clean_data(input_file: str, output_file: Optional[str],
               remove_duplicates: bool, remove_na: bool,
               max_workers: int, file_extensions: str):
    """数据清理功能 - 清理和预处理数据文件，删除空值和重复项"""

    input_path = Path(input_file)
    extensions = [ext.strip().lower() for ext in file_extensions.split(',')]

    # 收集要处理的文件
    files_to_process = []

    if input_path.is_file():
        # 单个文件
        if input_path.suffix.lower().lstrip('.') in extensions:
            files_to_process.append(input_path)
        else:
            raise UnsupportedFileFormatError(
                str(input_path),
                [f".{ext}" for ext in extensions]
            )
    elif input_path.is_dir():
        # 目录处理
        click.echo(f"扫描目录: {input_path}")
        for ext in extensions:
            pattern = f"*.{ext}"
            files_to_process.extend(input_path.rglob(pattern))

        if not files_to_process:
            click.echo(click.style(f"在目录 {input_path} 中未找到支持的文件", fg='yellow'))
            return
    else:
        raise FileProcessingError(f"路径不存在: {input_path}")

    click.echo(f"找到 {len(files_to_process)} 个文件需要处理")

    # 创建清理配置
    cleaning_config = {
        'remove_duplicates': remove_duplicates,
        'remove_na': remove_na,
        'output_base': output_file
    }

    # 使用异步任务队列处理文件
    try:
        results = run_async_task(
            process_files_concurrently(
                files_to_process,
                cleaning_config,
                max_workers
            )
        )

        # 显示处理结果
        display_cleaning_results(results)

    except Exception as e:
        raise FileProcessingError(f"批量处理失败: {str(e)}")