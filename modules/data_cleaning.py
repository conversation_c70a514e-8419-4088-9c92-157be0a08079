import os

import click
from typing import Optional, Dict

from core.queue_task import QueueConfig, AsyncTaskQueue
from core.registry import registry
from core.exceptions import *
from core.setting import tool_settings
from core.log import setup_logger
logger = setup_logger(__name__)

@click.command()
@click.argument('input_path', type=click.Path( readable=True))
@click.option('-o', '--output', 'output_path',
              type=click.Path(writable=True),
              help='输出文件路径')
@handle_exception
def clean_data(input_path: str, output_path: Optional[str]):
    """数据清理功能 - 清理和预处理数据文件"""
    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)
    # 判断material_path是否存在
    if not os.path.exists(input_path):
        raise FilePathNotFoundError(input_path)

    password_detected_path = os.path.join(output_path, "encrypted_document")
    error_files_path = os.path.join(output_path, "error_files")
    result_path = os.path.join(output_path, "result")
    for path in [password_detected_path, error_files_path, result_path]:
        os.makedirs(path, exist_ok=True)

    task_data = []
    def append_task_data(filepath: str):
        task_data.append({
            'file_path': filepath,
            'password_detected_path': password_detected_path,
            'error_files_path': error_files_path,
            'result_path': result_path
        })
    # 如果file_path是文件而不是目录，则直接处理该文件
    if os.path.isfile(input_path):
        append_task_data(input_path)
    else:
        ext_blacklist = [".meta"]
        for root, dirs, files in os.walk(input_path):
            for file in files:
                # 检查文件后缀是否在黑名单中
                if file.endswith(tuple(ext_blacklist)):
                    continue
                    # 检查文件是否可访问
                file_path = os.path.join(root, file)
                if not os.access(file_path, os.R_OK):
                    logger.error(f"无法访问文件: {file_path}")
                    continue
                append_task_data(str(file_path))

        # 配置队列
    config = QueueConfig(
        max_workers=tool_settings.tool_config.limits,
        max_retries=2,
        progress_description="数据清洗任务",
        show_progress=True,
        show_detailed_progress=True
    )

    # 创建队列并处理任务
    queue = AsyncTaskQueue(config)
    click.echo("数据清理完成")


async def rename_file(task_data: Dict[str, str]):
    """重命名文件, 检测密码， 提取邮件中的附件"""
    file_path = task_data['file_path']
    password_detected_path = task_data['password_detected_path']
    error_files_path = task_data['error_files_path']
    result_path = task_data['result_path']
    # 检测密码
    print(file_path)

# 注册模块
registry.register(
    'data_cleaning',
    '数据清理',
    '清理和预处理数据文件，删除空值和重复项',
    clean_data
)