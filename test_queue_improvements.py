#!/usr/bin/env python3
"""
队列任务改进功能测试脚本
展示新的进度条、统计和错误处理功能
"""

import sys
import time
import random
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.queue_task import AsyncTaskQueue, QueueConfig, TaskStatus, run_async_task


def simulate_data_processing(data):
    """模拟数据处理任务"""
    # 随机处理时间
    processing_time = random.uniform(0.1, 0.5)
    time.sleep(processing_time)
    
    # 模拟一些任务失败
    if random.random() < 0.1:  # 10% 失败率
        raise Exception(f"处理数据 '{data}' 时发生模拟错误")
    
    return f"已处理: {data} (耗时: {processing_time:.2f}s)"


async def async_data_processing(data):
    """模拟异步数据处理任务"""
    # 随机处理时间
    processing_time = random.uniform(0.1, 0.3)
    await asyncio.sleep(processing_time)
    
    # 模拟一些任务失败
    if random.random() < 0.15:  # 15% 失败率
        raise Exception(f"异步处理数据 '{data}' 时发生模拟错误")
    
    return f"异步已处理: {data} (耗时: {processing_time:.2f}s)"


def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 基本功能测试 ===")
    
    # 创建测试数据
    test_data = [f"数据项_{i}" for i in range(20)]
    
    # 配置队列
    config = QueueConfig(
        max_workers=4,
        max_retries=2,
        progress_description="基本功能测试",
        show_progress=True,
        show_detailed_progress=True
    )
    
    # 创建队列并处理任务
    queue = AsyncTaskQueue(config)
    
    async def run_test():
        results = await queue.process_tasks(simulate_data_processing, test_data)
        return results
    
    results = run_async_task(run_test())
    
    # 显示结果摘要
    queue.display_summary()
    
    # 分析结果
    success_count = sum(1 for r in results if r.status == TaskStatus.SUCCESS)
    failed_count = sum(1 for r in results if r.status == TaskStatus.FAILED)
    
    print(f"\n结果分析:")
    print(f"  成功: {success_count}/{len(results)}")
    print(f"  失败: {failed_count}/{len(results)}")
    
    return success_count > 0


def test_async_functionality():
    """测试异步功能"""
    print("\n=== 异步功能测试 ===")
    
    # 创建测试数据
    test_data = [f"异步数据_{i}" for i in range(15)]
    
    # 配置队列
    config = QueueConfig(
        max_workers=3,
        max_retries=1,
        progress_description="异步任务处理",
        show_progress=True
    )
    
    # 创建队列并处理任务
    queue = AsyncTaskQueue(config)
    
    async def run_test():
        results = await queue.process_tasks(async_data_processing, test_data)
        return results
    
    results = run_async_task(run_test())
    
    # 显示结果摘要
    queue.display_summary()
    
    success_count = sum(1 for r in results if r.status == TaskStatus.SUCCESS)
    return success_count > 0


def test_error_handling():
    """测试错误处理和重试机制"""
    print("\n=== 错误处理和重试测试 ===")
    
    def failing_task(data):
        """故意失败的任务"""
        if "fail" in data:
            raise Exception(f"任务 {data} 故意失败")
        time.sleep(0.1)
        return f"成功处理: {data}"
    
    # 创建包含失败任务的测试数据
    test_data = [
        "正常数据_1", "fail_data_1", "正常数据_2", 
        "fail_data_2", "正常数据_3", "正常数据_4"
    ]
    
    # 配置队列（高重试次数）
    config = QueueConfig(
        max_workers=2,
        max_retries=3,
        retry_delay=0.2,
        progress_description="错误处理测试",
        show_progress=True
    )
    
    queue = AsyncTaskQueue(config)
    
    async def run_test():
        results = await queue.process_tasks(failing_task, test_data)
        return results
    
    results = run_async_task(run_test())
    
    # 显示结果摘要
    queue.display_summary()
    
    # 分析重试情况
    retry_results = [r for r in results if r.retry_count > 0]
    print(f"\n重试分析:")
    print(f"  有重试的任务: {len(retry_results)}")
    for result in retry_results:
        print(f"    任务 {result.task_id}: 重试 {result.retry_count} 次, 状态: {result.status.value}")
    
    return len(results) > 0


def test_performance_monitoring():
    """测试性能监控功能"""
    print("\n=== 性能监控测试 ===")
    
    def variable_time_task(data):
        """处理时间可变的任务"""
        # 根据数据模拟不同的处理时间
        if "slow" in data:
            time.sleep(0.5)
        elif "fast" in data:
            time.sleep(0.05)
        else:
            time.sleep(0.2)
        return f"处理完成: {data}"
    
    # 创建不同类型的测试数据
    test_data = [
        "fast_task_1", "fast_task_2", "normal_task_1", 
        "slow_task_1", "normal_task_2", "fast_task_3",
        "slow_task_2", "normal_task_3", "normal_task_4"
    ]
    
    config = QueueConfig(
        max_workers=3,
        progress_description="性能监控测试",
        show_progress=True,
        enable_statistics=True
    )
    
    queue = AsyncTaskQueue(config)
    
    async def run_test():
        results = await queue.process_tasks(variable_time_task, test_data)
        return results
    
    results = run_async_task(run_test())
    
    # 显示详细统计
    queue.display_summary()
    
    # 分析执行时间分布
    execution_times = [r.execution_time for r in results if r.execution_time]
    if execution_times:
        print(f"\n执行时间分析:")
        print(f"  最快: {min(execution_times):.2f}秒")
        print(f"  最慢: {max(execution_times):.2f}秒")
        print(f"  平均: {sum(execution_times)/len(execution_times):.2f}秒")
    
    return len(results) > 0


def main():
    """主测试函数"""
    print("队列任务改进功能测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("基本功能测试", test_basic_functionality),
        ("异步功能测试", test_async_functionality),
        ("错误处理和重试测试", test_error_handling),
        ("性能监控测试", test_performance_monitoring),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n开始执行: {test_name}")
            result = test_func()
            results.append((test_name, result))
            print(f"测试结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！队列任务系统工作正常。")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")


if __name__ == "__main__":
    main()
